import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Simple configuration for Agent Vault functionality
  webpack: (config, { isServer }) => {
    // Basic fallbacks for browser environment
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        crypto: false,
      };
    }

    return config;
  },

  // Skip ESLint during build for now
  eslint: {
    ignoreDuringBuilds: true,
  },
};

export default nextConfig;
