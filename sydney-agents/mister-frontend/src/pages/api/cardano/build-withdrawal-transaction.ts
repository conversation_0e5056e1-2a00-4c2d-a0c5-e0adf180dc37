import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { fromAddress, toAddress, amount, contractAddress, redeemer } = req.body;

    console.log('🏦 Building withdrawal transaction...');
    console.log('📍 Contract:', contractAddress);
    console.log('👤 From:', fromAddress);
    console.log('👤 To:', toAddress);
    console.log('💰 Amount:', amount, 'lovelace');

    // For testing purposes, we'll create a simple withdrawal transaction
    // In production, this would:
    // 1. Query the vault UTxOs from the contract address
    // 2. Build a proper script transaction with redeemer
    // 3. Include the user's signature requirement

    // For now, return a test transaction that simulates withdrawal
    // This will help us test the frontend flow
    
    console.log('⚠️ TESTING MODE: Creating mock withdrawal transaction');
    console.log('🔍 In production, this would query actual vault UTxOs and build script transaction');

    // Mock CBOR for testing - this would be a real transaction in production
    const mockCborHex = "84a400818258201234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef00018182581d60" + 
                       fromAddress.slice(4, 60) + // Extract address bytes
                       "1a" + amount.toString(16).padStart(8, '0') + // Amount in hex
                       "021a0001e240" + // Fee: 123456 lovelace
                       "a0f5"; // Empty metadata and auxiliary data

    console.log('✅ Mock withdrawal transaction built');
    console.log('🔍 Mock CBOR:', mockCborHex);

    res.status(200).json({
      success: true,
      cborHex: mockCborHex,
      message: 'Withdrawal transaction built (TEST MODE)',
      warning: 'This is a test transaction. In production, this would interact with the actual smart contract.'
    });

  } catch (error) {
    console.error('❌ Error building withdrawal transaction:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
