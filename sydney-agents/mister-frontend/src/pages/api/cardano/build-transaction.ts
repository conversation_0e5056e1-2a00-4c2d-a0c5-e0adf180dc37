import { NextApiRequest, NextApiResponse } from 'next';

interface TransactionRequest {
  fromAddress: string;
  toAddress: string;
  amount: number; // in lovelace
  datum?: any;
  metadata?: any;
}

interface TransactionResponse {
  success: boolean;
  cborHex?: string;
  error?: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<TransactionResponse>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  try {
    const { fromAddress, toAddress, amount, datum, metadata }: TransactionRequest = req.body;

    console.log('🔨 Building Cardano transaction via Blockfrost...');
    console.log(`💰 From: ${fromAddress.substring(0, 20)}...`);
    console.log(`💰 To: ${toAddress.substring(0, 20)}...`);
    console.log(`💰 Amount: ${amount / 1000000} ADA`);

    // Use Blockfrost API to build the transaction
    const blockfrostProjectId = process.env.BLOCKFROST_PROJECT_ID || 'mainnetKDR7gGfvHy85Mqr4nYtfjoXq7fX8R1Bu';
    
    // Step 1: Get UTxOs from the sender address
    const utxosResponse = await fetch(`https://cardano-mainnet.blockfrost.io/api/v0/addresses/${fromAddress}/utxos`, {
      headers: {
        'project_id': blockfrostProjectId
      }
    });

    if (!utxosResponse.ok) {
      throw new Error(`Failed to fetch UTxOs: ${utxosResponse.statusText}`);
    }

    const utxos = await utxosResponse.json();
    
    if (!utxos || utxos.length === 0) {
      throw new Error('No UTxOs found at sender address');
    }

    // Step 2: Get protocol parameters
    const protocolResponse = await fetch('https://cardano-mainnet.blockfrost.io/api/v0/epochs/latest/parameters', {
      headers: {
        'project_id': blockfrostProjectId
      }
    });

    if (!protocolResponse.ok) {
      throw new Error(`Failed to fetch protocol parameters: ${protocolResponse.statusText}`);
    }

    const protocolParams = await protocolResponse.json();

    // Step 3: Build REAL CBOR transaction using Cardano Serialization Library
    console.log('🔧 Using Cardano Serialization Library to build REAL CBOR...');

    // Import CSL for Node.js backend
    const CSL = await import('@emurgo/cardano-serialization-lib-nodejs');

    // Create transaction builder
    const txBuilder = CSL.TransactionBuilder.new(
      CSL.LinearFee.new(
        CSL.BigNum.from_str(protocolParams.min_fee_a.toString()),
        CSL.BigNum.from_str(protocolParams.min_fee_b.toString())
      ),
      CSL.BigNum.from_str(protocolParams.min_utxo.toString()),
      CSL.BigNum.from_str(protocolParams.pool_deposit.toString()),
      CSL.BigNum.from_str(protocolParams.key_deposit.toString()),
      protocolParams.max_val_size,
      protocolParams.max_tx_size
    );

    // Add inputs from UTxOs
    let totalInput = CSL.BigNum.from_str('0');
    for (const utxo of utxos.slice(0, 2)) {
      const txHash = CSL.TransactionHash.from_bytes(Buffer.from(utxo.tx_hash, 'hex'));
      const txInput = CSL.TransactionInput.new(txHash, utxo.output_index);

      // Calculate input value
      const inputValue = CSL.Value.new(CSL.BigNum.from_str(utxo.amount[0].quantity));
      totalInput = totalInput.checked_add(CSL.BigNum.from_str(utxo.amount[0].quantity));

      const txOutput = CSL.TransactionOutput.new(
        CSL.Address.from_bech32(fromAddress),
        inputValue
      );

      txBuilder.add_input(txInput, txOutput);
    }

    // Add output
    const outputAddress = CSL.Address.from_bech32(toAddress);
    const outputValue = CSL.Value.new(CSL.BigNum.from_str(amount.toString()));
    const txOutput = CSL.TransactionOutput.new(outputAddress, outputValue);

    // Add datum if provided
    if (datum) {
      const plutusData = CSL.PlutusData.from_bytes(Buffer.from(JSON.stringify(datum), 'utf8'));
      txOutput.set_plutus_data(plutusData);
    }

    txBuilder.add_output(txOutput);

    // Set TTL
    const ttl = Math.floor(Date.now() / 1000) + 3600;
    txBuilder.set_ttl(ttl);

    // Add metadata if provided
    if (metadata) {
      const auxData = CSL.AuxiliaryData.new();
      const generalMetadata = CSL.GeneralTransactionMetadata.new();

      for (const [key, value] of Object.entries(metadata)) {
        const metadataValue = CSL.encode_json_str_to_metadatum(JSON.stringify(value), 0);
        generalMetadata.insert(CSL.BigNum.from_str(key), metadataValue);
      }

      auxData.set_metadata(generalMetadata);
      txBuilder.set_auxiliary_data(auxData);
    }

    // Build the transaction body
    const txBody = txBuilder.build();

    // Create transaction with empty witness set (wallet will add signatures)
    const witnessSet = CSL.TransactionWitnessSet.new();
    const transaction = CSL.Transaction.new(txBody, witnessSet);

    // Convert to CBOR hex
    const cborHex = Buffer.from(transaction.to_bytes()).toString('hex');

    console.log('✅ REAL CBOR transaction built successfully using CSL!');
    console.log('📋 CBOR length:', cborHex.length, 'characters');

    res.status(200).json({
      success: true,
      cborHex: cborHex
    });

  } catch (error) {
    console.error('❌ Transaction building failed:', error);
    
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
