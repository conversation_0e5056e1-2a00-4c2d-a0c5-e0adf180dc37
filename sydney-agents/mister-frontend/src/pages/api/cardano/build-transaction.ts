import { NextApiRequest, NextApiResponse } from 'next';

interface TransactionRequest {
  fromAddress: string;
  toAddress: string;
  amount: number; // in lovelace
  datum?: any;
  metadata?: any;
}

interface TransactionResponse {
  success: boolean;
  cborHex?: string;
  error?: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<TransactionResponse>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  try {
    const { fromAddress, toAddress, amount, datum, metadata }: TransactionRequest = req.body;

    console.log('🔨 Building Cardano transaction via Blockfrost...');
    console.log(`💰 From: ${fromAddress.substring(0, 20)}...`);
    console.log(`💰 To: ${toAddress.substring(0, 20)}...`);
    console.log(`💰 Amount: ${amount / 1000000} ADA`);

    // Convert hex address to bech32 if needed
    let bech32FromAddress = fromAddress;
    if (!fromAddress.startsWith('addr1')) {
      console.log('🔄 Converting hex address to bech32...');
      const CSL = await import('@emurgo/cardano-serialization-lib-nodejs');
      const addressBytes = Buffer.from(fromAddress, 'hex');
      const address = CSL.Address.from_bytes(addressBytes);
      bech32FromAddress = address.to_bech32();
      console.log(`✅ Converted to bech32: ${bech32FromAddress.substring(0, 20)}...`);
    }

    // Use Blockfrost API to build the transaction
    const blockfrostProjectId = process.env.BLOCKFROST_PROJECT_ID || 'mainnetKDR7gGfvHy85Mqr4nYtfjoXq7fX8R1Bu';
    
    // Step 1: Get UTxOs from the sender address (using bech32 format)
    const utxosResponse = await fetch(`https://cardano-mainnet.blockfrost.io/api/v0/addresses/${bech32FromAddress}/utxos`, {
      headers: {
        'project_id': blockfrostProjectId
      }
    });

    if (!utxosResponse.ok) {
      throw new Error(`Failed to fetch UTxOs: ${utxosResponse.statusText}`);
    }

    const utxos = await utxosResponse.json();
    
    if (!utxos || utxos.length === 0) {
      throw new Error('No UTxOs found at sender address');
    }

    // Step 2: Get protocol parameters
    const protocolResponse = await fetch('https://cardano-mainnet.blockfrost.io/api/v0/epochs/latest/parameters', {
      headers: {
        'project_id': blockfrostProjectId
      }
    });

    if (!protocolResponse.ok) {
      throw new Error(`Failed to fetch protocol parameters: ${protocolResponse.statusText}`);
    }

    const protocolParams = await protocolResponse.json();

    // Step 3: Build REAL CBOR transaction using CSL
    console.log('🔧 Building REAL CBOR transaction using CSL...');

    const CSL = await import('@emurgo/cardano-serialization-lib-nodejs');

    // Create transaction builder with protocol parameters
    const txBuilder = CSL.TransactionBuilder.new(
      CSL.TransactionBuilderConfigBuilder.new()
        .fee_algo(CSL.LinearFee.new(
          CSL.BigNum.from_str(protocolParams.min_fee_a.toString()),
          CSL.BigNum.from_str(protocolParams.min_fee_b.toString())
        ))
        .pool_deposit(CSL.BigNum.from_str(protocolParams.pool_deposit))
        .key_deposit(CSL.BigNum.from_str(protocolParams.key_deposit))
        .coins_per_utxo_word(CSL.BigNum.from_str(protocolParams.coins_per_utxo_size))
        .max_value_size(parseInt(protocolParams.max_val_size))
        .max_tx_size(parseInt(protocolParams.max_tx_size))
        .build()
    );

    // Add inputs from UTxOs
    let totalInput = CSL.BigNum.from_str('0');
    for (const utxo of utxos.slice(0, 3)) { // Use up to 3 UTxOs
      const inputValue = CSL.BigNum.from_str(utxo.amount[0].quantity);
      totalInput = totalInput.checked_add(inputValue);

      const input = CSL.TransactionInput.new(
        CSL.TransactionHash.from_bytes(Buffer.from(utxo.tx_hash, 'hex')),
        utxo.output_index
      );

      const output = CSL.TransactionOutput.new(
        CSL.Address.from_bech32(bech32FromAddress),
        CSL.Value.new(inputValue)
      );

      txBuilder.add_input(input, output);

      // Break if we have enough for the transaction
      if (totalInput.compare(CSL.BigNum.from_str((amount + 2000000).toString())) >= 0) {
        break;
      }
    }

    // Add output to contract address
    const outputValue = CSL.Value.new(CSL.BigNum.from_str(amount.toString()));
    const contractAddr = CSL.Address.from_bech32(toAddress);

    let txOutput = CSL.TransactionOutput.new(contractAddr, outputValue);

    // Add datum if provided
    if (datum) {
      const datumHash = CSL.hash_plutus_data(
        CSL.PlutusData.from_json(JSON.stringify(datum), CSL.PlutusDatumSchema.DetailedSchema)
      );
      txOutput.set_datum(CSL.Datum.new_data_hash(datumHash));
    }

    txBuilder.add_output(txOutput);

    // Add metadata if provided
    if (metadata) {
      const auxData = CSL.AuxiliaryData.new();
      const generalMetadata = CSL.GeneralTransactionMetadata.new();

      for (const [key, value] of Object.entries(metadata)) {
        const metadataValue = CSL.encode_json_str_to_metadatum(
          JSON.stringify(value),
          CSL.MetadataJsonSchema.DetailedSchema
        );
        generalMetadata.insert(
          CSL.BigNum.from_str(key),
          metadataValue
        );
      }

      auxData.set_metadata(generalMetadata);
      txBuilder.set_auxiliary_data(auxData);
    }

    // Set TTL (1 hour from now)
    const currentSlot = Math.floor(Date.now() / 1000) - 1596491091; // Shelley era start
    txBuilder.set_ttl(currentSlot + 3600);

    // Build the transaction
    const txBody = txBuilder.build();
    const tx = CSL.Transaction.new(
      txBody,
      CSL.TransactionWitnessSet.new()
    );

    // Convert to CBOR hex - THIS IS REAL CBOR!
    const cborHex = Buffer.from(tx.to_bytes()).toString('hex');

    console.log('✅ REAL CBOR transaction built successfully using CSL!');
    console.log('📋 CBOR length:', cborHex.length, 'characters');
    console.log('🔥 This is ACTUAL CBOR that Vespr can sign, not fake JSON!');

    res.status(200).json({
      success: true,
      cborHex: cborHex
    });

  } catch (error) {
    console.error('❌ Transaction building failed:', error);
    
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
