import { NextApiRequest, NextApiResponse } from 'next';

const BLOCKFROST_PROJECT_ID = process.env.BLOCKFROST_PROJECT_ID || 'mainnetKDR7gGfvHy85Mqr4nYtfjoXq7fX8R1Bu';
const BLOCKFROST_BASE_URL = 'https://cardano-mainnet.blockfrost.io/api/v0';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { cborHex, unsignedTxHex, witnessSetHex } = req.body;

    if (!cborHex && !witnessSetHex) {
      return res.status(400).json({ error: 'Missing cborHex or witnessSetHex parameter' });
    }

    let finalCborHex = cborHex;

    // If we have separate unsigned tx and witness set, reconstruct the complete transaction
    if (witnessSetHex && unsignedTxHex) {
      console.log('🔄 Reconstructing complete signed transaction from witness set...');
      console.log('🔍 Unsigned tx length:', unsignedTxHex.length);
      console.log('🔍 Witness set length:', witnessSetHex.length);

      try {
        // Import Cardano Serialization Library (server-side)
        const CardanoWasm = require('@emurgo/cardano-serialization-lib-nodejs');

        // Parse the original unsigned transaction
        const unsignedTxBytes = Buffer.from(unsignedTxHex, 'hex');
        const unsignedTx = CardanoWasm.Transaction.from_bytes(unsignedTxBytes);

        // Parse the witness set from Vespr
        const witnessBytes = Buffer.from(witnessSetHex, 'hex');
        const witnessSet = CardanoWasm.TransactionWitnessSet.from_bytes(witnessBytes);

        // Create the complete signed transaction
        const signedTransaction = CardanoWasm.Transaction.new(
          unsignedTx.body(),
          witnessSet,
          unsignedTx.auxiliary_data()
        );

        // Convert to hex for submission
        finalCborHex = Buffer.from(signedTransaction.to_bytes()).toString('hex');

        console.log('✅ Successfully reconstructed complete signed transaction');
        console.log('🔍 Complete signed tx length:', finalCborHex.length);

      } catch (reconstructionError) {
        console.error('❌ Transaction reconstruction failed:', reconstructionError);
        return res.status(400).json({
          error: 'Transaction reconstruction failed',
          details: reconstructionError.message
        });
      }
    }

    console.log('🔥 Submitting transaction to Cardano mainnet via Blockfrost...');
    console.log('🔍 Final CBOR length:', finalCborHex.length);
    console.log('🔍 CBOR preview:', finalCborHex.substring(0, 100) + '...');

    // Submit transaction to Blockfrost
    const submitResponse = await fetch(`${BLOCKFROST_BASE_URL}/tx/submit`, {
      method: 'POST',
      headers: {
        'project_id': BLOCKFROST_PROJECT_ID,
        'Content-Type': 'application/cbor',
      },
      body: Buffer.from(finalCborHex, 'hex'),
    });

    if (!submitResponse.ok) {
      const errorText = await submitResponse.text();
      console.error('❌ Blockfrost submission failed:', errorText);
      
      try {
        const errorJson = JSON.parse(errorText);
        return res.status(submitResponse.status).json({
          error: 'Transaction submission failed',
          details: errorJson,
          status: submitResponse.status
        });
      } catch {
        return res.status(submitResponse.status).json({
          error: 'Transaction submission failed',
          details: errorText,
          status: submitResponse.status
        });
      }
    }

    const txHash = await submitResponse.text();
    console.log('✅ Transaction submitted successfully! Hash:', txHash);

    return res.status(200).json({
      success: true,
      txHash: txHash.replace(/"/g, ''), // Remove quotes if present
      message: 'Transaction submitted to Cardano mainnet'
    });

  } catch (error) {
    console.error('❌ Transaction submission error:', error);
    return res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : String(error)
    });
  }
}
