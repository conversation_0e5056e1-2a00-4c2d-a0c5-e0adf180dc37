import { NextApiRequest, NextApiResponse } from 'next';

const BLOCKFROST_PROJECT_ID = process.env.BLOCKFROST_PROJECT_ID || 'mainnetKDR7gGfvHy85Mqr4nYtfjoXq7fX8R1Bu';
const BLOCKFROST_BASE_URL = 'https://cardano-mainnet.blockfrost.io/api/v0';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { cborHex } = req.body;

    if (!cborHex) {
      return res.status(400).json({ error: 'Missing cborHex parameter' });
    }

    console.log('🔥 Submitting transaction to Cardano mainnet via Blockfrost...');
    console.log('🔍 CBOR length:', cborHex.length);
    console.log('🔍 CBOR preview:', cborHex.substring(0, 100) + '...');

    // Submit transaction to Blockfrost
    const submitResponse = await fetch(`${BLOCKFROST_BASE_URL}/tx/submit`, {
      method: 'POST',
      headers: {
        'project_id': BLOCKFROST_PROJECT_ID,
        'Content-Type': 'application/cbor',
      },
      body: Buffer.from(cborHex, 'hex'),
    });

    if (!submitResponse.ok) {
      const errorText = await submitResponse.text();
      console.error('❌ Blockfrost submission failed:', errorText);
      
      try {
        const errorJson = JSON.parse(errorText);
        return res.status(submitResponse.status).json({
          error: 'Transaction submission failed',
          details: errorJson,
          status: submitResponse.status
        });
      } catch {
        return res.status(submitResponse.status).json({
          error: 'Transaction submission failed',
          details: errorText,
          status: submitResponse.status
        });
      }
    }

    const txHash = await submitResponse.text();
    console.log('✅ Transaction submitted successfully! Hash:', txHash);

    return res.status(200).json({
      success: true,
      txHash: txHash.replace(/"/g, ''), // Remove quotes if present
      message: 'Transaction submitted to Cardano mainnet'
    });

  } catch (error) {
    console.error('❌ Transaction submission error:', error);
    return res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : String(error)
    });
  }
}
