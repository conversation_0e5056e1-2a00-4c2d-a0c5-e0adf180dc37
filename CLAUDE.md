# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

MRSTRIKE is a comprehensive Mastra-based AI agent system for cryptocurrency trading and analysis, featuring multiple specialized agents for different trading strategies and market analysis. The system integrates with Cardano blockchain, Strike Finance, and various trading platforms.

## Key Components

### 1. Sydney Agents (`sydney-agents/`)
The main Mastra-based agent system containing:
- **Agents**: Specialized AI agents for trading strategies and analysis
- **Services**: Core trading and blockchain interaction services
- **Tools**: Custom tools for data analysis and execution
- **Workflows**: Automated trading and analysis workflows

### 2. MISTER Frontend (`sydney-agents/mister-frontend/`)
Next.js-based frontend application for:
- Trading interface and dashboard
- Wallet management and agent vault creation
- Real-time market data visualization
- Backtesting results display

### 3. MMISTERMMCP (`MMISTERMMCP/`)
Legacy MCP (Model Context Protocol) server implementation with:
- Trading bot functionality
- Performance tracking
- User settings management

## Common Development Commands

### Sydney Agents (Main System)
```bash
cd sydney-agents

# Development
npm run dev          # Start Mastra development server
npm run build        # Build the project
npm run start        # Start production server

# Agent Testing
npm run mister       # Start main MISTER agent server
npm run mister:simple # Start simplified MISTER server
npm run mister:demo  # Run demo mode
npm run mister:bridge # Start bridge server
```

### Frontend Development
```bash
cd sydney-agents/mister-frontend

# Development
npm run dev          # Start Next.js development server (with Turbopack)
npm run build        # Build Next.js application
npm run start        # Start production server
npm run lint         # Run ESLint
```

### Legacy MCP System
```bash
cd MMISTERMMCP

# Development
npm run start        # Start portfolio swap trading
npm run mcp          # Start MCP server
npm run dev          # Start frontend development
```

## Architecture Overview

### Agent System
The system uses Mastra framework with specialized agents:
- **Strike Agent**: Main trading agent for Strike Finance integration
- **Fibonacci Agent**: Fibonacci-based trading strategies
- **Multi-Timeframe Agent**: Multi-timeframe analysis
- **ADA Custom Algorithm Agent**: Custom Cardano trading algorithms
- **Sone Agent**: Personal assistant with voice capabilities
- **Cash Agent**: Cash management and analysis
- **Backtesting Agent**: Strategy backtesting and validation

### Services Architecture
Core services handle different aspects of trading:
- **Strike Finance API**: Direct Strike Finance integration
- **Cardano Balance Service**: Blockchain balance monitoring
- **Vault Trading Service**: Automated vault trading operations
- **Unified Execution Service**: Centralized trade execution
- **Fee Calculator**: Trading fee analysis and optimization

### Frontend Architecture
Next.js application with:
- **API Routes**: Backend endpoints for trading operations (`src/app/api/`)
- **Components**: Reusable UI components (`src/components/`)
- **Pages**: Application pages and routing (`src/app/`)
- **Services**: Client-side API integration (`src/lib/api/`)

## Key Configuration Files

- `sydney-agents/mastra.config.js`: Mastra framework configuration
- `sydney-agents/src/mastra/index.ts`: Main agent system initialization
- `sydney-agents/mister-frontend/next.config.ts`: Next.js configuration
- `MMISTERMMCP/railway.toml`: Railway deployment configuration

## Testing and Development

### Agent Testing
```bash
# Test specific agents
node sydney-agents/misterlabs/tests/test-sone-voice.js
node sydney-agents/misterlabs/tests/test-trading-monitor.js
node sydney-agents/test-vault-trading-flow.js
```

### Frontend Testing
Use the built-in test pages:
- `/test-agent-vault`: Agent vault functionality testing
- `/test-strike`: Strike Finance integration testing
- `/backtest-results`: Backtesting results display

### Backend Testing
```bash
# Test trading flows
node test-vault-trading-flow.js
node test-managed-wallet-flow.js
```

## Database and Storage

The system uses multiple database configurations:
- **Mastra Storage**: LibSQL in-memory database for agent state
- **SQLite Files**: Various `.db` files for persistent storage
- **Supabase**: User preferences and authentication (frontend)

## External Integrations

- **Strike Finance**: Primary trading platform integration
- **Cardano Blockchain**: Native Cardano operations via CSL
- **Blockfrost**: Cardano blockchain data provider
- **Google AI**: Gemini models for agent intelligence
- **TradingView**: Chart data and analysis
- **Railway**: Production deployment platform

## Development Notes

- The system requires Node.js 18+ and uses TypeScript throughout
- Mastra framework handles agent orchestration and workflow management
- Frontend uses modern React patterns with shadcn/ui components
- All trading operations include comprehensive fee analysis
- Security-focused with user isolation and secure transaction signing

## Production Deployment

The system is configured for Railway deployment with:
- Cloudflare integration for the Mastra agents
- Next.js frontend deployment
- Environment variable management for API keys and configurations